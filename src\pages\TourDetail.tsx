import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import ReviewsSection from '@/components/reviews/ReviewsSection';
import TourRouteMap from '@/components/tours/TourRouteMap';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar, Clock, MapPin, Star, Users, Heart, Share, Loader2 } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Tour } from '@/types/firebase';
import { useToast } from '@/hooks/use-toast';

const TourDetail = () => {
  const { id } = useParams();
  const { toast } = useToast();
  const [tour, setTour] = useState<Tour | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState(0);

  useEffect(() => {
    const loadTour = async () => {
      if (!id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const tourData = await FirebaseService.getTour(id);
        if (tourData) {
          setTour(tourData as Tour);
        } else {
          toast({
            title: "Tour not found",
            description: "The requested tour could not be found.",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error('Error loading tour:', error);
        toast({
          title: "Error loading tour",
          description: "There was an error loading the tour details.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadTour();
  }, [id, toast]);

  if (loading) {
    return (
      <div className="min-h-screen">
        <Header />
        <main className="pt-20 flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading tour details...</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!tour) {
    return (
      <div className="min-h-screen">
        <Header />
        <main className="pt-20 flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Tour Not Found</h1>
            <p className="text-gray-600 mb-4">The requested tour could not be found.</p>
            <Link to="/tours">
              <Button>Back to Tours</Button>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  const RatingStars = ({ rating, size = 'w-4 h-4' }: { rating: number; size?: string }) => (
    <div className="flex">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`${size} ${star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
        />
      ))}
    </div>
  );

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        {/* Hero Image Gallery */}
        <div className="relative h-96 md:h-[500px]">
          <img
            src={tour.images && tour.images.length > 0 
              ? `https://images.unsplash.com/${tour.images[selectedImage]}?auto=format&fit=crop&w=1200&h=500`
              : 'https://images.unsplash.com/photo-1472396961693-142e6e269027?auto=format&fit=crop&w=1200&h=500'
            }
            alt={tour.title}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-40" />
          <div className="absolute bottom-4 left-4 right-4">
            {tour.images && tour.images.length > 0 && (
              <div className="flex space-x-2 overflow-x-auto">
                {tour.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 ${
                      selectedImage === index ? 'border-white' : 'border-transparent'
                    }`}
                  >
                    <img
                      src={`https://images.unsplash.com/${image}?auto=format&fit=crop&w=100&h=100`}
                      alt={`${tour.title} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* Tour Header */}
              <div className="mb-6">
                <div className="flex flex-wrap gap-2 mb-3">
                  <Badge variant="secondary">{tour.category || 'Safari'}</Badge>
                  <Badge variant="outline">{tour.accommodationLevel || 'Standard'}</Badge>
                  <Badge variant={tour.difficulty === 'easy' ? 'default' : tour.difficulty === 'moderate' ? 'secondary' : 'destructive'}>
                    {tour.difficulty || 'Moderate'}
                  </Badge>
                </div>
                <h1 className="text-3xl md:text-4xl font-bold mb-4">{tour.title}</h1>
                <div className="flex flex-wrap gap-4 text-gray-600 mb-4">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    {tour.duration}
                  </div>
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    {tour.minGroupSize}-{tour.maxGroupSize} people
                  </div>
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2" />
                    {tour.destinations ? tour.destinations.join(', ') : tour.location}
                  </div>
                  <div className="flex items-center">
                    <RatingStars rating={tour.rating || 0} />
                    <span className="ml-1">{tour.rating || 0} ({tour.reviewCount || 0} reviews)</span>
                  </div>
                </div>
                <p className="text-lg text-gray-700">{tour.description}</p>
              </div>

              {/* Tabs */}
              <Tabs defaultValue="itinerary" className="w-full">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value="itinerary">Itinerary</TabsTrigger>
                  <TabsTrigger value="inclusions">Details</TabsTrigger>
                  <TabsTrigger value="gallery">Gallery</TabsTrigger>
                  <TabsTrigger value="reviews">Reviews</TabsTrigger>
                  <TabsTrigger value="route">Route Map</TabsTrigger>
                </TabsList>

                <TabsContent value="itinerary" className="space-y-4">
                  {tour.itinerary && tour.itinerary.length > 0 ? (
                    tour.itinerary.map((day, index) => (
                      <Card key={index}>
                        <CardHeader>
                          <CardTitle className="flex items-center">
                            <span className="bg-orange-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 text-sm">
                              {day.day}
                            </span>
                            {day.title}
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-gray-700 mb-3">{day.description}</p>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                              <strong>Activities:</strong>
                              <ul className="mt-1">
                                {day.activities?.map((activity, i) => (
                                  <li key={i} className="text-gray-600">
                                    • {typeof activity === 'string' ? activity : activity.activity}
                                  </li>
                                ))}
                              </ul>
                            </div>
                            <div>
                              <strong>Accommodation:</strong>
                              <p className="text-gray-600 mt-1">{day.accommodation}</p>
                            </div>
                            <div>
                              <strong>Meals:</strong>
                              <p className="text-gray-600 mt-1">{day.meals?.join(', ')}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    <Card>
                      <CardContent className="p-6 text-center text-gray-500">
                        Detailed itinerary will be provided upon booking.
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                <TabsContent value="inclusions" className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-green-600">What's Included</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {tour.includes?.map((item, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-green-600 mr-2">✓</span>
                              {item}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-red-600">What's Not Included</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {tour.excludes?.map((item, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-red-600 mr-2">✗</span>
                              {item}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  </div>

                  {tour.fitnessRequirements && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Fitness Requirements</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="mb-2"><strong>Level:</strong> {tour.fitnessRequirements.level}</p>
                        <p className="mb-2"><strong>Description:</strong> {tour.fitnessRequirements.description}</p>
                        <p className="mb-2"><strong>Walking Distance:</strong> {tour.fitnessRequirements.walkingDistance}</p>
                        <p><strong>Terrain:</strong> {tour.fitnessRequirements.terrain}</p>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                <TabsContent value="gallery">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {tour.images?.map((image, index) => (
                      <div key={index} className="aspect-square rounded-lg overflow-hidden">
                        <img
                          src={`https://images.unsplash.com/${image}?auto=format&fit=crop&w=400&h=400`}
                          alt={`Gallery ${index + 1}`}
                          className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                        />
                      </div>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="reviews">
                  <ReviewsSection tourId={tour.id} tourName={tour.title} />
                </TabsContent>

                <TabsContent value="route">
                  <TourRouteMap tour={tour} />
                </TabsContent>
              </Tabs>
            </div>

            {/* Booking Sidebar */}
            <div className="lg:col-span-1">
              <Card className="sticky top-4">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="text-3xl font-bold text-orange-600">
                        ${tour.price.toLocaleString()}
                      </div>
                      <div className="text-gray-600">per person</div>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="icon">
                        <Heart className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <Share className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Link to={`/book/${tour.id}`}>
                    <Button className="w-full bg-orange-600 hover:bg-orange-700 text-lg py-6">
                      Book This Tour
                    </Button>
                  </Link>
                  
                  <Link to="/tour-builder">
                    <Button variant="outline" className="w-full">
                      Customize This Tour
                    </Button>
                  </Link>

                  <div className="border-t pt-4">
                    <h4 className="font-semibold mb-2">Need Help?</h4>
                    <p className="text-sm text-gray-600 mb-3">
                      Speak with our safari experts for personalized advice
                    </p>
                    <Button variant="outline" className="w-full">
                      <Calendar className="h-4 w-4 mr-2" />
                      Schedule a Call
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default TourDetail;
